#-----------
# Database
#-----------
DatabaseDriver=com.mysql.cj.jdbc.Driver
DatabaseURL=**********************************************
DbUserName=hnbuw
DbPassword=misyn8b

MinimumIdle=10
MaximumPoolSize=50
IdleTimeout=300000
MaxLifetime=1800000
ConnectionTimeout=30000
LeakDetectionThreshold=2000
ValidationTimeout=5000
redis-host=localhost




#-----------
# Contact
#-----------
MailAddress=<EMAIL>



#-----------
# Template File Path
#-----------
Path=D:/home/<USER>/
TempFileDirectory=D:/home/<USER>/temp/




#------------------
# Record List setting
#------------------
NoRecordPerPage=100
CompanyTitle=HNB General Insurance



ActiveMQBroker=tcp://************:61616
ActiveMQUser=admin
ActiveMQPassword=admin

MailSendUser=LOLC-MCMS
AppUrl=http://lolcdc1cmsapp:8080/mcms/
InternalAppUrl=http://lolcdc1cmsapp:8080/mcms/
SyncAppUrl=http://localhost:9080/mcms_sync/


ImageResizePercent=0.85
ImageCompressionQualityFactor=0.7
TheftClaimPeriod=180
DocumentNotificationTimeout=10
AriNotificationTimeout=1
Profile=${env}


KeycloakServerUrl=https://hnbuwauth.misynergy.com/
KeycloakRealm=hnb-general
KeycloakClientId=hnb-mcms-dev
KeycloakClientSecret=icI8FQTZrN480RZCDCAReEoa8WkRmyT1

#AuthServiceLogoutUrl=https://hnbmcmsauth.misynergy.com/api/logout
#DocumentServiceApiUrl=http://storage-service-api:8080/api
AuthServiceLogoutUrl=http://localhost:8082/api/logout
DocumentServiceApiUrl=http://localhost:9090/api
ClaimDocumentDirectory=claim-documents

# OnMiSite keycloak token details
TokenClientId=mcms-onmisite-webapp-dev
TokenClientSecret=E9Xpb585oXHpkg6J4c6peg8MsA9iSJLf
TokenContentType=application/x-www-form-urlencoded
TokenGrantType=password
TokenUsername=rtechexe
TokenPassword=123456$
TokenUrl=https://hnbuwauth.misynergy.com/realms/hnb-general/protocol/openid-connect/token

# OnMiSite service call details
SaveEndpointUrl=http://localhost:8080/misyn/api/v1/jobs/save
SaveEndpointContentType=application/json
AdminEndpointUrl=http://localhost:8080/misyn/admin-view/

